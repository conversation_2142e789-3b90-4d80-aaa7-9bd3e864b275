.common-font {
  font-size: 12px;
  line-height: 18px;
  color: #333;
}

.common-block {
  padding: 1px 4px;
  border-radius: 2px;
  background: #eee;
}

.right-icon {
  position: absolute;
  right: 10px;
  top: 50%;
}

.container {
  position: relative;
  padding: 5px 25px 5px 6px !important;
  border: 1px solid #d8d8d8;
  border-radius: 2px;
  min-height: 32px;
  background: #fff;
  display: flex;
  overflow: hidden;
  gap: 5px;

  :global {
    .placeholder {
      line-height: 18px;
      color: #bbb;
      margin-left: 4px;
    }
  }

  .arrow {
    .right-icon;

    transition: transform 0.3s;
    transform: scaleX(1.4) translateY(-5px) !important;
    color: rgba(0, 0, 0, 0.25);
  }

  &:not(.disabled):hover {
    border-color: #3cabfa;

    .arrow {
      color: #128bed;
    }
  }

  &.disabled {
    background: transparent !important;
    cursor: not-allowed;

    .omittedCount {
      color: #bbb;
    }

    .placeholder {
      color: #666;
    }
  }
}

.focused {
  border-color: #3cabfa;

  .arrow {
    color: #128bed;
    transition: transform 0.3s;
    transform: scaleX(1.34) rotate(180deg) translateY(5px) !important;
  }
}

.label {
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
  cursor: pointer;
  .common-block;
  .common-font;

  &:not(.disabled):hover {
    background: #e2f1fd;

    .removeIcon {
      color: #128bed;
    }
  }

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #eee;
    color: #bbb;
  }
}

.removeIcon {
  transform: rotate(45deg);
  color: #999;
}

.drop-wapper {
  :global {
    .ant-checkbox {
      pointer-events: none;
    }

    .ant-checkbox-wrapper {
      width: 100%;
    }

    .ant-dropdown-menu {
      max-height: 400px;
      overflow-y: auto;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    }

    .ant-dropdown-menu-item:hover {
      .ant-checkbox-wrapper {
        color: #128bed;
      }
    }
  }
}

.omittedCount {
  .common-block;
  .common-font;

  cursor: pointer;

  &:hover {
    color: #128bed;
    background: #e2f1fd !important;
  }
}

.dot {
  border-radius: 50%;
  display: inline-flex;
  width: 6px;
  height: 6px;
  margin-right: 4px;
}

.tooltip {
  :global {
    .ant-tooltip-inner {
      max-width: 300px;
      padding: 5px 10px;
    }

    .ant-tooltip-content .ant-tooltip-inner > * {
      background: none;
      padding: 0;
      border: none;
      border-radius: 0;
    }
  }
}

.clearIcon {
  .right-icon;

  right: 30px;
  transform: translateY(-50%);
  color: #d8d8d8;

  &:hover {
    color: #999;
  }
}

.selectTooltip {
  max-width: 400px;

  :global {
    .ant-tooltip-content .ant-tooltip-inner {
      padding: 8px 10px;

      & > div {
        padding: 0;
      }

      & > * {
        background-color: #fff;
        border: none;
      }
    }
  }
}
